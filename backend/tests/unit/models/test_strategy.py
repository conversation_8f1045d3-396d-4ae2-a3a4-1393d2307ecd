"""
Test cases for strategy model classes.
"""

import os
import sys
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

import numpy as np
import pandas as pd
import pytest

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))

from src.models.strategy import Stock


class TestStock:
    """Test cases for Stock dataclass."""

    def test_stock_initialization_basic(self):
        """Test basic Stock initialization."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0)
        
        assert stock.symbol == "AAPL"
        assert stock.shares == 100
        assert stock.avg_price == 150.0
        assert stock.position_type == "LONG"
        assert stock.volatility == 0.0
        assert stock.beta == 1.0
        assert stock.current_price is None
        assert isinstance(stock.entry_date, datetime)

    def test_stock_initialization_with_entry_date(self):
        """Test Stock initialization with custom entry date."""
        entry_date = datetime(2024, 1, 1)
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0, entry_date=entry_date)
        
        assert stock.entry_date == entry_date

    def test_stock_initialization_invalid_shares(self):
        """Test Stock initialization with invalid shares."""
        with pytest.raises(ValueError, match="Shares must be positive number"):
            Stock(symbol="AAPL", shares=0, avg_price=150.0)
        
        with pytest.raises(ValueError, match="Shares must be positive number"):
            Stock(symbol="AAPL", shares=-10, avg_price=150.0)

    def test_stock_initialization_short_position(self):
        """Test Stock initialization with short position."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0, position_type="SHORT")
        
        assert stock.position_type == "SHORT"

    def test_current_value_property_with_current_price(self):
        """Test current_value property when current_price is set."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0, current_price=160.0)
        
        assert stock.current_value == 16000.0  # 100 * 160

    def test_current_value_property_short_position(self):
        """Test current_value property for short position."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0, current_price=160.0, position_type="SHORT")
        
        assert stock.current_value == -16000.0  # -(100 * 160)

    def test_current_value_property_no_current_price(self):
        """Test current_value property when current_price is None."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0)
        
        # Should fallback to cost basis
        assert stock.current_value == 15000.0  # 100 * 150

    def test_unrealized_pnl_property(self):
        """Test unrealized_pnl property."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0)
        stock.unrealized_pl = 1000.0
        
        assert stock.unrealized_pnl == 1000.0

    def test_position_value_property_long(self):
        """Test position_value property for long position."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0)
        
        assert stock.position_value == 15000.0  # 100 * 150

    def test_position_value_property_short(self):
        """Test position_value property for short position."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0, position_type="SHORT")
        
        assert stock.position_value == -15000.0  # -(100 * 150)

    def test_days_held_property(self):
        """Test days_held property."""
        entry_date = datetime.now() - timedelta(days=30)
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0, entry_date=entry_date)
        
        assert stock.days_held == 30

    def test_risk_contribution_property(self):
        """Test risk_contribution property."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0, volatility=0.2, beta=1.5)
        
        expected = 15000.0 * 0.2 * 1.5  # position_value * volatility * beta
        assert stock.risk_contribution == expected

    def test_quantity_property(self):
        """Test quantity property (alias for shares)."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0)
        
        assert stock.quantity == 100

    def test_update_risk_metrics_basic(self):
        """Test basic update_risk_metrics functionality."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0)
        
        # Create sample market data
        dates = pd.date_range(start="2024-01-01", periods=100, freq="D")
        market_data = pd.DataFrame({
            "Close": np.random.normal(150, 10, 100),
            "Market_Return": np.random.normal(0.001, 0.02, 100)
        }, index=dates)
        
        stock.update_risk_metrics(160.0, market_data)
        
        assert stock.current_price == 160.0
        assert stock.unrealized_pl == 1000.0  # (160 - 150) * 100
        assert stock.unrealized_pl_pct == pytest.approx(0.0667, rel=1e-3)  # 1000 / 15000
        assert stock.volatility > 0
        assert isinstance(stock.last_updated, datetime)

    def test_update_risk_metrics_short_position(self):
        """Test update_risk_metrics for short position."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0, position_type="SHORT")
        
        market_data = pd.DataFrame({
            "Close": [150, 155, 160],
        })
        
        stock.update_risk_metrics(160.0, market_data)
        
        assert stock.unrealized_pl == -1000.0  # (150 - 160) * 100

    def test_update_risk_metrics_empty_market_data(self):
        """Test update_risk_metrics with empty market data."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0)
        
        empty_data = pd.DataFrame()
        stock.update_risk_metrics(160.0, empty_data)
        
        # Should still update PnL
        assert stock.current_price == 160.0
        assert stock.unrealized_pl == 1000.0

    def test_update_risk_metrics_insufficient_data(self):
        """Test update_risk_metrics with insufficient market data."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0)
        
        insufficient_data = pd.DataFrame({"Close": [150]})
        stock.update_risk_metrics(160.0, insufficient_data)
        
        # Should still update PnL
        assert stock.current_price == 160.0
        assert stock.unrealized_pl == 1000.0

    def test_update_risk_metrics_skip_recent_update(self):
        """Test that update_risk_metrics skips if recently updated."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0)
        stock.last_updated = datetime.now() - timedelta(minutes=30)  # 30 minutes ago
        
        market_data = pd.DataFrame({"Close": [150, 155, 160]})
        
        # Should skip update
        stock.update_risk_metrics(160.0, market_data)
        
        # current_price should not be updated
        assert stock.current_price is None

    def test_update_risk_metrics_exception_handling(self):
        """Test update_risk_metrics handles exceptions gracefully."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0)
        
        # Create data that might cause calculation errors
        market_data = pd.DataFrame({
            "Close": [np.nan, np.inf, -np.inf],
        })
        
        # Should not raise exception
        stock.update_risk_metrics(160.0, market_data)
        
        assert stock.current_price == 160.0

    def test_calculate_historical_volatility_no_data(self):
        """Test _calculate_historical_volatility with no historical data."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0)
        
        result = stock._calculate_historical_volatility()
        assert result == 0.0

    def test_calculate_historical_volatility_insufficient_data(self):
        """Test _calculate_historical_volatility with insufficient data."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0)
        stock._historical_data = pd.DataFrame({"Close": [150]})
        
        result = stock._calculate_historical_volatility()
        assert result == 0.0

    def test_calculate_historical_volatility_success(self):
        """Test _calculate_historical_volatility with sufficient data."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0)
        
        # Create sample historical data
        dates = pd.date_range(start="2024-01-01", periods=100, freq="D")
        stock._historical_data = pd.DataFrame({
            "Close": np.random.normal(150, 10, 100)
        }, index=dates)
        
        result = stock._calculate_historical_volatility()
        assert result > 0
        assert isinstance(result, float)

    def test_portfolio_risk_metrics_empty_list(self):
        """Test portfolio_risk_metrics with empty stock list."""
        result = Stock.portfolio_risk_metrics([])
        
        expected = {
            "portfolio_beta": 0.0,
            "portfolio_volatility": 0.0,
            "concentration_risk": 0.0,
            "value_at_risk": 0.0
        }
        assert result == expected

    def test_portfolio_risk_metrics_single_stock(self):
        """Test portfolio_risk_metrics with single stock."""
        stock = Stock(symbol="AAPL", shares=100, avg_price=150.0, volatility=0.2, beta=1.5)
        
        result = Stock.portfolio_risk_metrics([stock])
        
        assert result["portfolio_beta"] == 1.5
        assert result["portfolio_volatility"] == 0.2
        assert result["concentration_risk"] == 1.0  # 100% concentration
        assert result["value_at_risk"] > 0

    def test_portfolio_risk_metrics_multiple_stocks(self):
        """Test portfolio_risk_metrics with multiple stocks."""
        stock1 = Stock(symbol="AAPL", shares=100, avg_price=150.0, volatility=0.2, beta=1.5)
        stock2 = Stock(symbol="GOOGL", shares=50, avg_price=200.0, volatility=0.25, beta=1.2)
        
        result = Stock.portfolio_risk_metrics([stock1, stock2])
        
        assert isinstance(result["portfolio_beta"], float)
        assert isinstance(result["portfolio_volatility"], float)
        assert isinstance(result["concentration_risk"], float)
        assert isinstance(result["value_at_risk"], float)
        assert 0 < result["concentration_risk"] <= 1.0

    def test_portfolio_risk_metrics_zero_value_portfolio(self):
        """Test portfolio_risk_metrics with zero value portfolio."""
        # Create a stock with very small position value that would trigger the error handling
        stock = Stock(symbol="AAPL", shares=1, avg_price=0.0, volatility=0.2, beta=1.5)

        result = Stock.portfolio_risk_metrics([stock])

        # Should return zeros for all metrics when total value is zero
        for key in ["portfolio_beta", "portfolio_volatility", "concentration_risk", "value_at_risk"]:
            assert result[key] == 0.0

    def test_portfolio_risk_metrics_with_short_positions(self):
        """Test portfolio_risk_metrics with short positions."""
        stock1 = Stock(symbol="AAPL", shares=100, avg_price=150.0, volatility=0.2, beta=1.5, position_type="LONG")
        stock2 = Stock(symbol="GOOGL", shares=50, avg_price=200.0, volatility=0.25, beta=1.2, position_type="SHORT")
        
        result = Stock.portfolio_risk_metrics([stock1, stock2])
        
        # Should handle short positions correctly
        assert isinstance(result["portfolio_beta"], float)
        assert isinstance(result["portfolio_volatility"], float)
        assert isinstance(result["concentration_risk"], float)
        assert isinstance(result["value_at_risk"], float)
