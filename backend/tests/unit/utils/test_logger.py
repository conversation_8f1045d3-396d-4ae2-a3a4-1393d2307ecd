"""
Test cases for logger utility functions.
"""

import os
import tempfile
import shutil
from unittest.mock import patch, MagicMock
import pytest


class TestLogger:
    """Test cases for logger utility."""

    def setup_method(self):
        """Setup test environment before each test."""
        # Import here to avoid circular imports
        import sys
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))
        
        from src.utils.logger import get_logger, configure_logger, cleanup_logger, log_exceptions
        
        self.get_logger = get_logger
        self.configure_logger = configure_logger
        self.cleanup_logger = cleanup_logger
        self.log_exceptions = log_exceptions
        
        # Create temporary log directory for testing
        self.temp_log_dir = tempfile.mkdtemp()

    def teardown_method(self):
        """Cleanup after each test."""
        if os.path.exists(self.temp_log_dir):
            shutil.rmtree(self.temp_log_dir, ignore_errors=True)
        
        # Reset logger configuration state
        import src.utils.logger as logger_module
        logger_module._IS_CONFIGURED = False
        logger_module._HANDLER_IDS = []

    @patch('src.utils.logger.log_path')
    def test_configure_logger(self, mock_log_path):
        """Test logger configuration."""
        mock_log_path.__str__ = lambda: self.temp_log_dir
        
        # Reset configuration state
        import src.utils.logger as logger_module
        logger_module._IS_CONFIGURED = False
        
        self.configure_logger()
        
        # Check that configuration was marked as complete
        assert logger_module._IS_CONFIGURED is True
        assert len(logger_module._HANDLER_IDS) >= 2  # At least file and console handlers

    def test_get_logger_basic(self):
        """Test basic logger retrieval."""
        logger = self.get_logger()
        assert logger is not None
        assert hasattr(logger, 'info')
        assert hasattr(logger, 'error')
        assert hasattr(logger, 'debug')
        assert hasattr(logger, 'warning')

    def test_get_logger_with_name(self):
        """Test logger retrieval with name."""
        logger = self.get_logger("test_module")
        assert logger is not None

    @patch.dict(os.environ, {'LOG_ENV': 'DEVELOPMENT'})
    def test_get_logger_development_env(self):
        """Test logger in development environment."""
        # Reset configuration state
        import src.utils.logger as logger_module
        logger_module._IS_CONFIGURED = False
        
        logger = self.get_logger()
        assert logger is not None

    @patch.dict(os.environ, {'LOG_ENV': 'PRODUCTION'})
    def test_get_logger_production_env(self):
        """Test logger in production environment."""
        # Reset configuration state
        import src.utils.logger as logger_module
        logger_module._IS_CONFIGURED = False
        
        logger = self.get_logger()
        assert logger is not None

    def test_cleanup_logger(self):
        """Test logger cleanup."""
        # This should not raise any exceptions
        self.cleanup_logger()

    def test_log_exceptions_decorator_success(self):
        """Test log_exceptions decorator with successful function."""
        @self.log_exceptions
        def test_function():
            return "success"
        
        result = test_function()
        assert result == "success"

    def test_log_exceptions_decorator_with_exception(self):
        """Test log_exceptions decorator with function that raises exception."""
        @self.log_exceptions
        def test_function():
            raise ValueError("Test error")
        
        with pytest.raises(ValueError, match="Test error"):
            test_function()

    def test_log_exceptions_decorator_preserves_function_metadata(self):
        """Test that log_exceptions decorator preserves function metadata."""
        @self.log_exceptions
        def test_function():
            """Test function docstring."""
            return "test"
        
        assert test_function.__name__ == "test_function"
        assert test_function.__doc__ == "Test function docstring."

    @patch('src.utils.logger.logger')
    def test_log_exceptions_logs_error(self, mock_logger):
        """Test that log_exceptions decorator logs errors."""
        @self.log_exceptions
        def test_function():
            raise RuntimeError("Test runtime error")
        
        with pytest.raises(RuntimeError):
            test_function()
        
        # Verify that logger.opt().error() was called
        mock_logger.opt.assert_called_once()

    @patch('os.makedirs')
    @patch('src.utils.logger.logger')
    def test_configure_logger_creates_log_directory(self, mock_logger, mock_makedirs):
        """Test that configure_logger creates log directory."""
        # Reset configuration state
        import src.utils.logger as logger_module
        logger_module._IS_CONFIGURED = False
        
        self.configure_logger()
        
        mock_makedirs.assert_called_once()

    def test_multiple_get_logger_calls(self):
        """Test multiple calls to get_logger."""
        logger1 = self.get_logger()
        logger2 = self.get_logger()
        
        # Both should be valid logger instances
        assert logger1 is not None
        assert logger2 is not None


class TestLoggerEdgeCases:
    """Test edge cases and error conditions for logger utility."""

    def setup_method(self):
        """Setup test environment before each test."""
        import sys
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "..", "..", "src"))
        
        from src.utils.logger import get_logger, configure_logger, cleanup_logger, log_exceptions
        
        self.get_logger = get_logger
        self.configure_logger = configure_logger
        self.cleanup_logger = cleanup_logger
        self.log_exceptions = log_exceptions

    def teardown_method(self):
        """Cleanup after each test."""
        # Reset logger configuration state
        import src.utils.logger as logger_module
        logger_module._IS_CONFIGURED = False
        logger_module._HANDLER_IDS = []

    def test_cleanup_logger_with_invalid_handlers(self):
        """Test cleanup_logger with invalid handler IDs."""
        import src.utils.logger as logger_module
        logger_module._HANDLER_IDS = [999, 1000]  # Invalid handler IDs
        
        # Should not raise any exceptions
        self.cleanup_logger()

    @patch('src.utils.logger.logger.remove')
    def test_cleanup_logger_exception_handling(self, mock_remove):
        """Test cleanup_logger handles exceptions gracefully."""
        mock_remove.side_effect = Exception("Test exception")
        
        import src.utils.logger as logger_module
        logger_module._HANDLER_IDS = [1, 2]
        
        # Should not raise any exceptions
        self.cleanup_logger()

    def test_log_exceptions_with_complex_function(self):
        """Test log_exceptions decorator with function that has arguments."""
        @self.log_exceptions
        def test_function(a, b, c=None):
            if c is None:
                return a + b
            return a + b + c
        
        result = test_function(1, 2)
        assert result == 3
        
        result = test_function(1, 2, c=3)
        assert result == 6

    def test_log_exceptions_with_kwargs(self):
        """Test log_exceptions decorator with function that uses **kwargs."""
        @self.log_exceptions
        def test_function(**kwargs):
            return sum(kwargs.values())
        
        result = test_function(a=1, b=2, c=3)
        assert result == 6
